<!DOCTYPE html>
<html lang="{{ current_lang }}" dir="{{ 'rtl' if current_lang == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ settings.shop_name }}{% endblock %}</title>
    
    <!-- Tailwind CSS with RTL support -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '{{ settings.primary_color }}',
                        secondary: '{{ settings.secondary_color }}'
                    },
                    fontFamily: {
                        'arabic': ['Cairo', 'Tajawal', 'sans-serif'],
                        'english': ['Inter', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS Variables -->
    <style>
        :root {
            --primary-color: {{ settings.primary_color }};
            --secondary-color: {{ settings.secondary_color }};
        }
        
        body {
            font-family: {{ "'Cairo', 'Tajawal', sans-serif" if current_lang == 'ar' else "'Inter', sans-serif" }};
        }
        
        .bg-primary { background-color: var(--primary-color); }
        .bg-secondary { background-color: var(--secondary-color); }
        .text-primary { color: var(--primary-color); }
        .text-secondary { color: var(--secondary-color); }
        .border-primary { border-color: var(--primary-color); }
        .border-secondary { border-color: var(--secondary-color); }
        
        /* RTL specific styles */
        [dir="rtl"] .rtl\:text-right { text-align: right; }
        [dir="rtl"] .rtl\:text-left { text-align: left; }
        [dir="rtl"] .rtl\:mr-auto { margin-right: auto; }
        [dir="rtl"] .rtl\:ml-auto { margin-left: auto; }
        
        /* Animation classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in-right {
            animation: slideInRight 0.3s ease-out;
        }
        
        @keyframes slideInRight {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
        
        .slide-in-left {
            animation: slideInLeft 0.3s ease-out;
        }
        
        @keyframes slideInLeft {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50" x-data="{ mobileMenuOpen: false, langMenuOpen: false }">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="{{ url_for('core.index') }}" class="flex items-center">
                        {% if settings.logo_b64 %}
                            <img src="{{ settings.logo_b64 }}" alt="{{ settings.shop_name }}" class="h-10 w-auto">
                        {% else %}
                            <i class="fas fa-store text-2xl text-primary {{ 'ml-2' if current_lang != 'ar' else 'mr-2' }}"></i>
                        {% endif %}
                        <span class="text-xl font-bold text-gray-900">{{ settings.shop_name }}</span>
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="flex items-center space-x-4 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
                        <a href="{{ url_for('core.index') }}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                            {{ _('الرئيسية') }}
                        </a>
                        <a href="{{ url_for('core.menu') }}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                            {{ _('القائمة') }}
                        </a>
                        <a href="{{ url_for('core.about') }}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                            {{ _('حول المتجر') }}
                        </a>
                        <a href="{{ url_for('core.contact') }}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                            {{ _('اتصل بنا') }}
                        </a>
                    </div>
                </div>
                
                <!-- Language Selector & Admin -->
                <div class="flex items-center space-x-2 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
                    <!-- Language Dropdown -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">
                            <i class="fas fa-globe {{ 'ml-1' if current_lang != 'ar' else 'mr-1' }}"></i>
                            {{ available_languages[current_lang] }}
                            <i class="fas fa-chevron-down {{ 'ml-1' if current_lang != 'ar' else 'mr-1' }} text-xs"></i>
                        </button>
                        
                        <div x-show="open" @click.away="open = false" 
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             class="absolute {{ 'right-0' if current_lang == 'ar' else 'left-0' }} mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                            {% for lang_code, lang_name in available_languages.items() %}
                                <a href="{{ url_for('core.set_language', lang=lang_code) }}" 
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 {{ 'text-primary font-medium' if lang_code == current_lang else '' }}">
                                    {{ lang_name }}
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <!-- Admin Link -->
                    {% if current_user.is_authenticated %}
                        <a href="{{ url_for('admin.dashboard') }}" class="bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-opacity-90 transition-colors">
                            {{ _('لوحة التحكم') }}
                        </a>
                    {% else %}
                        <a href="{{ url_for('admin.login') }}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">
                            {{ _('تسجيل الدخول') }}
                        </a>
                    {% endif %}
                    
                    <!-- Mobile menu button -->
                    <button @click="mobileMenuOpen = !mobileMenuOpen" class="md:hidden text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div x-show="mobileMenuOpen" 
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 transform -translate-y-2"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform -translate-y-2"
             class="md:hidden bg-white border-t">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="{{ url_for('core.index') }}" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">
                    {{ _('الرئيسية') }}
                </a>
                <a href="{{ url_for('core.menu') }}" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">
                    {{ _('القائمة') }}
                </a>
                <a href="{{ url_for('core.about') }}" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">
                    {{ _('حول المتجر') }}
                </a>
                <a href="{{ url_for('core.contact') }}" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">
                    {{ _('اتصل بنا') }}
                </a>
            </div>
        </div>
    </nav>
    
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="fixed top-20 {{ 'right-4' if current_lang == 'ar' else 'left-4' }} z-50 space-y-2" x-data="{ show: true }">
                {% for category, message in messages %}
                    <div x-show="show" 
                         x-transition:enter="transition ease-out duration-300"
                         x-transition:enter-start="opacity-0 transform translate-x-full"
                         x-transition:enter-end="opacity-100 transform translate-x-0"
                         x-transition:leave="transition ease-in duration-200"
                         x-transition:leave-start="opacity-100 transform translate-x-0"
                         x-transition:leave-end="opacity-0 transform translate-x-full"
                         class="max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden">
                        <div class="p-4">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    {% if category == 'success' %}
                                        <i class="fas fa-check-circle text-green-400"></i>
                                    {% elif category == 'error' %}
                                        <i class="fas fa-exclamation-circle text-red-400"></i>
                                    {% elif category == 'warning' %}
                                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                    {% else %}
                                        <i class="fas fa-info-circle text-blue-400"></i>
                                    {% endif %}
                                </div>
                                <div class="{{ 'mr-3' if current_lang == 'ar' else 'ml-3' }} w-0 flex-1 pt-0.5">
                                    <p class="text-sm font-medium text-gray-900">{{ message }}</p>
                                </div>
                                <div class="{{ 'ml-4' if current_lang == 'ar' else 'mr-4' }} flex-shrink-0 flex">
                                    <button @click="show = false" class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- Main Content -->
    <main class="flex-1">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-12">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">{{ settings.shop_name }}</h3>
                    <p class="text-gray-300">{{ _('أفضل الأطعمة والمشروبات في المدينة') }}</p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">{{ _('روابط سريعة') }}</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ url_for('core.index') }}" class="text-gray-300 hover:text-white transition-colors">{{ _('الرئيسية') }}</a></li>
                        <li><a href="{{ url_for('core.menu') }}" class="text-gray-300 hover:text-white transition-colors">{{ _('القائمة') }}</a></li>
                        <li><a href="{{ url_for('core.about') }}" class="text-gray-300 hover:text-white transition-colors">{{ _('حول المتجر') }}</a></li>
                        <li><a href="{{ url_for('core.contact') }}" class="text-gray-300 hover:text-white transition-colors">{{ _('اتصل بنا') }}</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">{{ _('تواصل معنا') }}</h3>
                    <div class="space-y-2 text-gray-300">
                        <p><i class="fas fa-phone {{ 'ml-2' if current_lang != 'ar' else 'mr-2' }}"></i> +20 123 456 789</p>
                        <p><i class="fas fa-envelope {{ 'ml-2' if current_lang != 'ar' else 'mr-2' }}"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt {{ 'ml-2' if current_lang != 'ar' else 'mr-2' }}"></i> {{ _('القاهرة، مصر') }}</p>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
                <p>&copy; 2024 {{ settings.shop_name }}. {{ _('جميع الحقوق محفوظة') }}.</p>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/menu.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
