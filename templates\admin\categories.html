{% extends "admin/base.html" %}

{% block page_title %}{{ _('الفئات') }}{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ _('إدارة الفئات') }}</h1>
            <p class="text-gray-600">{{ _('إدارة فئات المنتجات في متجرك') }}</p>
        </div>
        <a href="{{ url_for('admin.add_category') }}" 
           class="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors">
            <i class="fas fa-plus {{ 'ml-2' if current_lang != 'ar' else 'mr-2' }}"></i>
            {{ _('إضافة فئة جديدة') }}
        </a>
    </div>
    
    <!-- Categories Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        {% if categories %}
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-{{ 'right' if current_lang == 'ar' else 'left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {{ _('الفئة') }}
                    </th>
                    <th class="px-6 py-3 text-{{ 'right' if current_lang == 'ar' else 'left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {{ _('الوصف') }}
                    </th>
                    <th class="px-6 py-3 text-{{ 'right' if current_lang == 'ar' else 'left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {{ _('المنتجات') }}
                    </th>
                    <th class="px-6 py-3 text-{{ 'right' if current_lang == 'ar' else 'left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {{ _('الترتيب') }}
                    </th>
                    <th class="px-6 py-3 text-{{ 'right' if current_lang == 'ar' else 'left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {{ _('الحالة') }}
                    </th>
                    <th class="px-6 py-3 text-{{ 'right' if current_lang == 'ar' else 'left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {{ _('الإجراءات') }}
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for category in categories %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-full bg-primary flex items-center justify-center">
                                    <i class="{{ category.icon or 'fas fa-utensils' }} text-white"></i>
                                </div>
                            </div>
                            <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                                <div class="text-sm font-medium text-gray-900">
                                    {{ category.get_name(current_lang) }}
                                </div>
                                <div class="text-sm text-gray-500">
                                    {{ category.get_name('en' if current_lang == 'ar' else 'ar') }}
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-900 max-w-xs truncate">
                            {{ category.get_description(current_lang) or _('لا يوجد وصف') }}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            {{ category.products|length }} {{ _('منتج') }}
                        </div>
                        {% if category.subcategories %}
                        <div class="text-sm text-gray-500">
                            {{ category.subcategories|length }} {{ _('فئة فرعية') }}
                        </div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ category.sort_order }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if category.is_active %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{ _('نشط') }}
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                {{ _('غير نشط') }}
                            </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex items-center space-x-2 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
                            <a href="{{ url_for('admin.edit_category', id=category.id) }}" 
                               class="text-indigo-600 hover:text-indigo-900">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{{ url_for('core.category_detail', category_id=category.id) }}" 
                               class="text-green-600 hover:text-green-900" target="_blank">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                            {% if not category.products %}
                            <form method="POST" action="{{ url_for('admin.delete_category', id=category.id) }}" 
                                  class="inline" onsubmit="return confirm('{{ _('هل أنت متأكد من حذف هذه الفئة؟') }}')">
                                <button type="submit" class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                            {% else %}
                            <span class="text-gray-400" title="{{ _('لا يمكن حذف فئة تحتوي على منتجات') }}">
                                <i class="fas fa-trash"></i>
                            </span>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <i class="fas fa-tags text-4xl text-gray-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">{{ _('لا توجد فئات') }}</h3>
            <p class="text-gray-600 mb-6">{{ _('ابدأ بإضافة فئة جديدة لتنظيم منتجاتك') }}</p>
            <a href="{{ url_for('admin.add_category') }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-opacity-90">
                <i class="fas fa-plus {{ 'ml-2' if current_lang != 'ar' else 'mr-2' }}"></i>
                {{ _('إضافة فئة جديدة') }}
            </a>
        </div>
        {% endif %}
    </div>
    
    <!-- Statistics -->
    {% if categories %}
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-tags text-xl"></i>
                </div>
                <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                    <p class="text-sm font-medium text-gray-600">{{ _('إجمالي الفئات') }}</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ categories|length }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                    <p class="text-sm font-medium text-gray-600">{{ _('الفئات النشطة') }}</p>
                    <p class="text-2xl font-semibold text-gray-900">
                        {{ categories|selectattr('is_active')|list|length }}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-boxes text-xl"></i>
                </div>
                <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                    <p class="text-sm font-medium text-gray-600">{{ _('إجمالي المنتجات') }}</p>
                    <p class="text-2xl font-semibold text-gray-900">
                        {{ categories|sum(attribute='products')|length }}
                    </p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
