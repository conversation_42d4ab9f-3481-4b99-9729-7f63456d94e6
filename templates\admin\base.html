<!DOCTYPE html>
<html lang="{{ current_lang }}" dir="{{ 'rtl' if current_lang == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ _('لوحة التحكم') }} - {{ settings.shop_name }}{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '{{ settings.primary_color }}',
                        secondary: '{{ settings.secondary_color }}'
                    }
                }
            }
        }
    </script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: {{ "'Cairo', 'Tajawal', sans-serif" if current_lang == 'ar' else "'Inter', sans-serif" }};
        }
        
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        
        @media (max-width: 768px) {
            .sidebar-hidden {
                transform: translateX({{ '-100%' if current_lang != 'ar' else '100%' }});
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen" x-data="{ sidebarOpen: false }">
        <!-- Sidebar -->
        <div class="fixed inset-y-0 {{ 'right-0' if current_lang == 'ar' else 'left-0' }} z-50 w-64 bg-gray-800 sidebar-transition md:relative md:translate-x-0"
             :class="{ 'sidebar-hidden': !sidebarOpen }">
            
            <!-- Logo -->
            <div class="flex items-center justify-center h-16 bg-gray-900">
                <div class="flex items-center">
                    {% if settings.logo_b64 %}
                        <img src="{{ settings.logo_b64 }}" alt="{{ settings.shop_name }}" class="h-8 w-auto {{ 'ml-2' if current_lang != 'ar' else 'mr-2' }}">
                    {% else %}
                        <i class="fas fa-store text-white text-xl {{ 'ml-2' if current_lang != 'ar' else 'mr-2' }}"></i>
                    {% endif %}
                    <span class="text-white text-lg font-semibold">{{ _('لوحة التحكم') }}</span>
                </div>
            </div>
            
            <!-- Navigation -->
            <nav class="mt-8">
                <div class="px-4 space-y-2">
                    <a href="{{ url_for('admin.dashboard') }}" 
                       class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors {{ 'active' if request.endpoint == 'admin.dashboard' else '' }}">
                        <i class="fas fa-tachometer-alt {{ 'ml-3' if current_lang != 'ar' else 'mr-3' }}"></i>
                        {{ _('الرئيسية') }}
                    </a>
                    
                    <a href="{{ url_for('admin.settings') }}" 
                       class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors">
                        <i class="fas fa-cog {{ 'ml-3' if current_lang != 'ar' else 'mr-3' }}"></i>
                        {{ _('الإعدادات') }}
                    </a>
                    
                    <a href="{{ url_for('admin.categories') }}" 
                       class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors">
                        <i class="fas fa-tags {{ 'ml-3' if current_lang != 'ar' else 'mr-3' }}"></i>
                        {{ _('الفئات') }}
                    </a>
                    
                    <a href="#" 
                       class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors">
                        <i class="fas fa-boxes {{ 'ml-3' if current_lang != 'ar' else 'mr-3' }}"></i>
                        {{ _('المنتجات') }}
                    </a>
                    
                    <div class="border-t border-gray-700 my-4"></div>
                    
                    <a href="{{ url_for('core.index') }}" 
                       class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors">
                        <i class="fas fa-external-link-alt {{ 'ml-3' if current_lang != 'ar' else 'mr-3' }}"></i>
                        {{ _('عرض الموقع') }}
                    </a>
                    
                    <a href="{{ url_for('admin.logout') }}" 
                       class="flex items-center px-4 py-2 text-gray-300 hover:bg-red-600 hover:text-white rounded-md transition-colors">
                        <i class="fas fa-sign-out-alt {{ 'ml-3' if current_lang != 'ar' else 'mr-3' }}"></i>
                        {{ _('تسجيل الخروج') }}
                    </a>
                </div>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Bar -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center">
                        <button @click="sidebarOpen = !sidebarOpen" 
                                class="text-gray-500 hover:text-gray-700 md:hidden">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h1 class="text-xl font-semibold text-gray-900 {{ 'mr-4' if current_lang == 'ar' else 'ml-4' }} md:ml-0">
                            {% block page_title %}{{ _('لوحة التحكم') }}{% endblock %}
                        </h1>
                    </div>
                    
                    <div class="flex items-center space-x-4 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
                        <span class="text-sm text-gray-600">
                            {{ _('مرحباً') }}، {{ current_user.username }}
                        </span>
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-gray-600"></i>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <div class="p-4">
                        {% for category, message in messages %}
                            <div class="mb-4 p-4 rounded-md {% if category == 'success' %}bg-green-50 border border-green-200 text-green-800{% elif category == 'error' %}bg-red-50 border border-red-200 text-red-800{% elif category == 'warning' %}bg-yellow-50 border border-yellow-200 text-yellow-800{% else %}bg-blue-50 border border-blue-200 text-blue-800{% endif %}">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        {% if category == 'success' %}
                                            <i class="fas fa-check-circle"></i>
                                        {% elif category == 'error' %}
                                            <i class="fas fa-exclamation-circle"></i>
                                        {% elif category == 'warning' %}
                                            <i class="fas fa-exclamation-triangle"></i>
                                        {% else %}
                                            <i class="fas fa-info-circle"></i>
                                        {% endif %}
                                    </div>
                                    <div class="{{ 'mr-3' if current_lang == 'ar' else 'ml-3' }}">
                                        <p class="text-sm font-medium">{{ message }}</p>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endwith %}
            
            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- Sidebar Overlay for Mobile -->
    <div x-show="sidebarOpen" 
         @click="sidebarOpen = false"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 md:hidden"></div>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
